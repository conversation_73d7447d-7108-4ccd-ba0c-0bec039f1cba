{"name": "seanbehan-ca", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "npm run build && wrangler dev", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "deploy": "npm run build && wrangler deploy", "cf-typegen": "wrangler types && mv worker-configuration.d.ts src/"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250614.0", "@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@fontsource-variable/inter": "^5.2.6", "@skeletonlabs/skeleton": "^3.1.3", "@skeletonlabs/skeleton-svelte": "^1.2.3", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-cloudflare": "^7.0.4", "@sveltejs/enhanced-img": "^0.6.0", "@sveltejs/kit": "^2.21.5", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.2", "fuse.js": "^7.1.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "mdsvex": "^0.12.6", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.12", "svelte": "^5.34.3", "svelte-check": "^4.2.1", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5", "vitest": "^3.2.4", "wrangler": "^4.20.0"}}